using System.Reflection;
using OPCUAServer.Common;
using RolePermissionBasedAccess;
using Serilog;
using UnifiedAutomation.UaBase;
using UnifiedAutomation.UaServer;

namespace OPCUAServer.Devices.PAC3220;

/// <summary>
/// PAC3220NodeManager is a class that manages the PAC3220 system.
/// </summary>
internal partial class PAC3220NodeManager : BaseNodeManager
{
    private static readonly string PAC3220TemplateFileName = "PAC3220_Modified.xml";
    private static Dictionary<string, PAC3220System> _dictPAC3220System = new();
    private readonly ILogger _logger = LoggingHelper.GetLogger<PAC3220NodeManager>();

    public ushort InstanceNamespaceIndex { get; private set; }

    public ushort TypeNamespaceIndex { get; private set; }

    public string PAC3220ItemId { get; set; }

    public string PAC3220Name { get; set; } = "PAC3220";

    /// <summary>
    /// Initializes a new instance of the <see cref="PAC3220NodeManager"/> class.
    /// </summary>
    /// <param name="server">The server manager.</param>
    public PAC3220NodeManager(ServerManager server)
        : base(server)
    {
        // Namespace initialization moved to Startup() method
    }

    public static void InitialPAC3220System(int pacNumber, List<string> itemid)
    {
        _dictPAC3220System = itemid.Take(pacNumber)
                                   .ToDictionary(id => id, id => new PAC3220System());
    }

    /// <summary>
    /// Called when the node manager is started.
    /// </summary>
    public override void Startup()
    {
        try
        {
            this._logger.Information("Starting PAC3220NodeManager.");
            base.Startup();

            // Initialize namespaces
            this.TypeNamespaceIndex = this.AddNamespaceUri(Sentron.PAC3220.Namespaces.PAC3220);
            this.InstanceNamespaceIndex = this.AddNamespaceUri("http://sentron.org/PAC3220/" + this.PAC3220Name);

            // load the model.
            this._logger.Information($"Loading the PAC3220 Model: {this.PAC3220Name}.");
            this.ImportUaNodeset(Assembly.GetEntryAssembly(), PAC3220TemplateFileName);

            _dictPAC3220System[this.PAC3220ItemId].Initialize(this.PAC3220ItemId);

            try
            {
                var settings = new CreateObjectSettings()
                {
                    ParentNodeId = ObjectIds.ObjectsFolder,
                    ReferenceTypeId = ReferenceTypeIds.Organizes,
                    RequestedNodeId = new NodeId(this.PAC3220Name, this.InstanceNamespaceIndex),
                    BrowseName = new QualifiedName(this.PAC3220Name, this.InstanceNamespaceIndex),
                    TypeDefinitionId = ObjectTypeIds.FolderType
                };
                this.CreateObject(this.Server.DefaultRequestContext, settings);
            }
            catch (Exception ex)
            {
                this._logger.Error($"Failed to create PAC3220NodeManager: {ex.Message}.");
            }

            // Create controllers from configuration
            foreach (BlockConfiguration block in _dictPAC3220System[this.PAC3220ItemId].GetBlocks())
            {
                // set type definition NodeId
                NodeId typeDefinitionId = ObjectTypeIds.BaseObjectType;
                if (block.Type == BlockType.PAC3220)
                {
                    typeDefinitionId = new NodeId(Sentron.PAC3220.ObjectTypes.PAC3220, this.TypeNamespaceIndex);
                }

                // create object.
                var settings = new CreateObjectSettings()
                {
                    ParentNodeId = new NodeId(this.PAC3220Name, this.InstanceNamespaceIndex),
                    ReferenceTypeId = ReferenceTypeIds.Organizes,
                    RequestedNodeId = new NodeId(block.Name, this.InstanceNamespaceIndex),
                    BrowseName = new QualifiedName(block.Name, this.TypeNamespaceIndex),
                    TypeDefinitionId = typeDefinitionId
                };

                this.CreateObject(this.Server.DefaultRequestContext, settings);

                foreach (BlockProperty property in block.Properties)
                {
                    // the node was already created when the controller object was instantiated.
                    // this call links the node to the underlying system data.
                    VariableNode variable = this.SetVariableConfiguration(
                        new NodeId(block.Name, this.InstanceNamespaceIndex),
                        new QualifiedName(property.Name, this.TypeNamespaceIndex),
                        NodeHandleType.ExternalPolled,
                        new SystemAddress() { Address = block.Address, Offset = property.Offset });

                    if (variable != null)
                    {
                        lock (this.InMemoryNodeLock)
                        {
                            variable.AccessLevel = property.Writeable ? AccessLevels.CurrentReadOrWrite : AccessLevels.CurrentRead;
                        }

                        this.SetNodePermissions(
                            new NodeId(block.Name, this.InstanceNamespaceIndex),
                            new QualifiedName(property.Name, this.TypeNamespaceIndex),
                            RolePermissionManager.RolePermissionTypeCollection);
                    }
                }
            }
        }
        catch (Exception e)
        {
            this._logger.Error($"Failed to start PAC3220 NodeManager: {e.Message}.");
        }
    }

    /// <summary>
    /// Called when the node manager is stopped.
    /// </summary>
    public override void Shutdown()
    {
        try
        {
            this._logger.Information("Stopping PAC3220NodeManager.");
            base.Shutdown();
        }
        catch (Exception e)
        {
            this._logger.Error($"Failed to stop PAC3220 NodeManager: {e.Message}.");
        }
    }

    /// <summary>
    /// Releases unmanaged and - optionally - managed resources.
    /// Disposes the PAC3220 system instance associated with this node manager.
    /// </summary>
    /// <param name="disposing">
    /// <c>true</c> to release both managed and unmanaged resources;
    /// <c>false</c> to release only unmanaged resources.
    /// </param>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _dictPAC3220System[this.PAC3220ItemId]?.Dispose();
        }
    }

    /// <summary>
    /// Reads the attributes.
    /// </summary>
    /// <param name="context">The context.</param>
    /// <param name="transaction">The transaction.</param>
    /// <param name="operationHandles">The operation handles.</param>
    /// <param name="settings">The settings.</param>
    protected override void Read(
        RequestContext context,
        TransactionHandle transaction,
        IList<NodeAttributeOperationHandle> operationHandles,
        IList<ReadValueId> settings)
    {
        for (int ii = 0; ii < operationHandles.Count; ii++)
        {
            DataValue? dv = null;

            if (operationHandles[ii].NodeHandle.UserData is SystemAddress address)
            {
                if (CannotPassNodeAccessChecks(context, operationHandles[ii].NodeHandle, UserAccessMask.Read, out StatusCode statusCode))
                {
                    dv = new DataValue(statusCode);
                }
                else
                {
                    object? value = _dictPAC3220System[this.PAC3220ItemId].Read(address.Address, address.Offset);
                    if (value is not null)
                    {
                        dv = new DataValue(new Variant(value, null), DateTime.UtcNow);

                        if (!string.IsNullOrEmpty(settings[ii].IndexRange) || !QualifiedName.IsNull(settings[ii].DataEncoding))
                        {
                            dv = this.ApplyIndexRangeAndEncoding(
                                operationHandles[ii].NodeHandle,
                                dv,
                                settings[ii].IndexRange,
                                settings[ii].DataEncoding);
                        }
                    }
                }
            }

            // set an error if not found.
            dv ??= new DataValue(new StatusCode(StatusCodes.BadNodeIdUnknown));

            ((ReadCompleteEventHandler)transaction.Callback)(
                operationHandles[ii],
                transaction.CallbackData,
                dv,
                true);
        }
    }

    /// <summary>
    /// Write the attributes
    /// </summary>
    /// <param name="context">The context.</param>
    /// <param name="transaction">The transaction.</param>
    /// <param name="operationHandles">The operation handles.</param>
    /// <param name="settings">The settings.</param>
    protected override void Write(
        RequestContext context,
        TransactionHandle transaction,
        IList<NodeAttributeOperationHandle> operationHandles,
        IList<WriteValue> settings)
    {
        for (int ii = 0; ii < operationHandles.Count; ii++)
        {
            StatusCode error = StatusCodes.BadNodeIdUnknown;

            if (operationHandles[ii].NodeHandle.UserData is SystemAddress address)
            {
                if (!string.IsNullOrEmpty(settings[ii].IndexRange))
                {
                    error = StatusCodes.BadIndexRangeInvalid;
                }
                else if (CannotPassNodeAccessChecks(context, operationHandles[ii].NodeHandle, UserAccessMask.Write, out StatusCode statusCode))
                {
                    error = statusCode;
                }
                else if (!_dictPAC3220System[PAC3220ItemId].Write(address.Address, address.Offset, settings[ii].Value.Value))
                {
                    error = StatusCodes.BadUserAccessDenied;
                }
                else
                {
                    error = StatusCodes.Good;
                }
            }

            // return the data to the caller.
            ((WriteCompleteEventHandler)transaction.Callback)(
                operationHandles[ii],
                transaction.CallbackData,
                error,
                true);
        }
    }
}
