2025-08-04 09:25:07.869 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:08.779 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:08.780 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:08.875 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:08.876 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:08.974 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:08.974 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:09.015 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:09.039 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:09.706 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:09.707 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:09.708 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:09.713 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:09.722 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:09.723 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:09.726 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:09.756 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:09.756 +08:00 [INF] Loading the PAC4200 Model: PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:09.796 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.824 +08:00 [INF] Adding dynamic property 'alert' to block 'PAC4200EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.825 +08:00 [ERR] Parent block 'PAC4200EventController' not found <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.827 +08:00 [INF] === Detailed Node Debug Info === <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.827 +08:00 [INF] Number of blocks in configuration: 2 <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.827 +08:00 [INF] Block config: PAC4200Controller (Type: 1, Address: 0) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.828 +08:00 [INF]   - Using InstanceNamespaceIndex (7): Found <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.828 +08:00 [INF]   - Using TypeNamespaceIndex (6): Not Found <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.829 +08:00 [INF]     Node details: ns=7;s=PAC4200Controller, BrowseName: 6:PAC4200Controller <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.829 +08:00 [INF] Block config: PAC4200EventController (Type: 2, Address: 1) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.829 +08:00 [INF]   - Using InstanceNamespaceIndex (7): Not Found <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.830 +08:00 [INF]   - Using TypeNamespaceIndex (6): Not Found <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.830 +08:00 [INF] === End Debug Info === <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.830 +08:00 [INF] Adding dynamic property 'alert' to block 'PAC4200EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.832 +08:00 [INF] ✅ Successfully created variable node 'alert' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.834 +08:00 [INF] Successfully write to blockAddress 1 and tag 0. <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.834 +08:00 [INF] Property 'alert' successfully integrated into system at address 1:0 <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:11.835 +08:00 [INF] Successfully added dynamic property 'alert' to block 'PAC4200EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:12.102 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:12.103 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:12.104 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:12.107 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:12.107 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:12.107 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:12.107 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:12.107 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:12.107 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:25:12.107 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:15.756 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:25.914 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:26.276 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:26.277 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:26.384 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:26.384 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:26.466 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:26.467 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:26.504 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:26.528 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:27.048 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:27.049 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:27.050 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:27.055 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:27.062 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:27.063 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:27.066 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:27.090 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:27.090 +08:00 [INF] Loading the PAC4200 Model: PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:27.129 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.152 +08:00 [INF] Adding dynamic property 'alert' to block 'PAC4200EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.153 +08:00 [INF] ✅ Successfully created variable node 'alert' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.154 +08:00 [INF] Successfully write to blockAddress 1 and tag 0. <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.154 +08:00 [INF] Property 'alert' successfully integrated into system at address 1:0 <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.154 +08:00 [INF] Successfully added dynamic property 'alert' to block 'PAC4200EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.369 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.369 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.370 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.373 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.373 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.373 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.374 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.374 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.374 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:50:29.374 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 09:51:19.227 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:42.232 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.093 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.094 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.199 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.200 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.287 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.287 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.323 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.344 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.937 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.939 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.940 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.945 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.952 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.953 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.955 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.980 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.980 +08:00 [INF] Loading the PAC4200 Model: PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.981 +08:00 [INF] Creating PAC4200 ObjectTypes programmatically (proof of concept)... <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.981 +08:00 [INF] Creating simplified PAC4200 ObjectType for proof of concept... <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.982 +08:00 [INF] Creating demonstration variable 'current' with value 98.2... <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.982 +08:00 [INF] ✅ Successfully created demonstration variable 'current' with value 98.2 <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.982 +08:00 [INF] ✅ Successfully created simplified PAC4200 ObjectType (proof of concept) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.983 +08:00 [INF] Successfully created PAC4200 ObjectTypes programmatically. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:43.992 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:46.014 +08:00 [INF] Creating block 'PAC4200Controller' using programmatically created PAC4200Controller ObjectType <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:46.015 +08:00 [ERR] Failed to start PAC4200NodeManager for PAC4200 143213: [BadTypeDefinitionInvalid]. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:46.245 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:46.245 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:46.246 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:46.249 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:46.249 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:46.249 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:46.249 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:46.250 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:46.250 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:04:46.250 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:05:17.737 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:30.853 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:31.845 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:31.845 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:31.961 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:31.961 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.047 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.047 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.080 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.099 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.604 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.605 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.607 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.612 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.619 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.620 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.623 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.646 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.646 +08:00 [INF] Creating PAC4200 Model programmatically: PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.647 +08:00 [INF] Creating PAC4200 ObjectTypes programmatically (proof of concept)... <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.647 +08:00 [INF] Creating PAC4200Controller ObjectType programmatically... <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.647 +08:00 [INF] Creating PAC4200Controller ObjectType... <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.648 +08:00 [INF] ✅ ObjectType added to address space with NodeId:  <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.648 +08:00 [INF] ✅ Successfully created PAC4200Controller ObjectType <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.648 +08:00 [INF] Creating 'current' variable template for PAC4200Controller... <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.649 +08:00 [ERR] ❌ Exception creating 'current' variable template: [BadParentNodeIdInvalid] <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
UnifiedAutomation.UaBase.StatusException: [BadParentNodeIdInvalid]
   at UnifiedAutomation.UaServer.BaseNodeManager.CreateVariable(RequestContext context, CreateVariableSettings settings)
   at OPCUAServer.Devices.PAC4200.PAC4200NodeManager.CreateCurrentVariableTemplate(NodeId parentObjectTypeId) in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200NodeManager.cs:line 232
2025-08-04 11:19:32.691 +08:00 [ERR] ❌ Exception creating PAC4200Controller ObjectType: [BadParentNodeIdInvalid] <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
UnifiedAutomation.UaBase.StatusException: [BadParentNodeIdInvalid]
   at UnifiedAutomation.UaServer.BaseNodeManager.CreateVariable(RequestContext context, CreateVariableSettings settings)
   at OPCUAServer.Devices.PAC4200.PAC4200NodeManager.CreateCurrentVariableTemplate(NodeId parentObjectTypeId) in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200NodeManager.cs:line 232
   at OPCUAServer.Devices.PAC4200.PAC4200NodeManager.CreatePAC4200ControllerObjectType() in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200NodeManager.cs:line 144
2025-08-04 11:19:32.692 +08:00 [ERR] ❌ Exception creating PAC4200 ObjectTypes: [BadParentNodeIdInvalid] <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
UnifiedAutomation.UaBase.StatusException: [BadParentNodeIdInvalid]
   at UnifiedAutomation.UaServer.BaseNodeManager.CreateVariable(RequestContext context, CreateVariableSettings settings)
   at OPCUAServer.Devices.PAC4200.PAC4200NodeManager.CreateCurrentVariableTemplate(NodeId parentObjectTypeId) in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200NodeManager.cs:line 232
   at OPCUAServer.Devices.PAC4200.PAC4200NodeManager.CreatePAC4200ControllerObjectType() in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200NodeManager.cs:line 144
   at OPCUAServer.Devices.PAC4200.PAC4200NodeManager.CreateSimplifiedPAC4200ObjectType() in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200NodeManager.cs:line 82
2025-08-04 11:19:32.692 +08:00 [ERR] Failed to create PAC4200 ObjectTypes programmatically: [BadParentNodeIdInvalid] <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
UnifiedAutomation.UaBase.StatusException: [BadParentNodeIdInvalid]
   at UnifiedAutomation.UaServer.BaseNodeManager.CreateVariable(RequestContext context, CreateVariableSettings settings)
   at OPCUAServer.Devices.PAC4200.PAC4200NodeManager.CreateCurrentVariableTemplate(NodeId parentObjectTypeId) in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200NodeManager.cs:line 232
   at OPCUAServer.Devices.PAC4200.PAC4200NodeManager.CreatePAC4200ControllerObjectType() in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200NodeManager.cs:line 144
   at OPCUAServer.Devices.PAC4200.PAC4200NodeManager.CreateSimplifiedPAC4200ObjectType() in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200NodeManager.cs:line 82
   at OPCUAServer.Devices.PAC4200.PAC4200NodeManager.CreatePAC4200ObjectTypes() in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200NodeManager.cs:line 60
2025-08-04 11:19:32.693 +08:00 [ERR] Failed to start PAC4200NodeManager for PAC4200 143213: [BadParentNodeIdInvalid]. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.903 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.904 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.905 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.907 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.908 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.908 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.908 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.908 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.908 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:19:32.908 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 11:31:00.766 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:32.527 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:33.386 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:33.386 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:33.501 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:33.502 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:33.591 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:33.591 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:33.627 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:33.649 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.178 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.180 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.181 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.187 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.194 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.195 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.198 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.221 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.221 +08:00 [INF] Creating PAC4200 Model programmatically: PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.221 +08:00 [INF] Creating PAC4200 ObjectTypes programmatically (proof of concept)... <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.221 +08:00 [INF] Creating PAC4200Controller ObjectType programmatically... <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.222 +08:00 [INF] Creating PAC4200Controller ObjectType with variable templates... <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.222 +08:00 [INF] ✅ ObjectType added to address space with NodeId:  <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.222 +08:00 [INF] ✅ PAC4200Controller ObjectType with variable templates created successfully <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.223 +08:00 [INF] Creating PAC4200EventController ObjectType... <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.223 +08:00 [INF] ✅ PAC4200EventController ObjectType added to address space with NodeId:  <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.223 +08:00 [INF] ✅ Successfully created PAC4200EventController ObjectType <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.224 +08:00 [INF] ✅ Successfully created PAC4200 ObjectTypes programmatically <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.224 +08:00 [INF] Successfully created PAC4200 ObjectTypes programmatically. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:34.231 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.252 +08:00 [INF] Creating block 'PAC4200Controller' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.254 +08:00 [INF] Processing PAC4200Controller variables for block 'PAC4200Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.254 +08:00 [INF] ✅ Created variable 'Ua' for block 'PAC4200Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.255 +08:00 [INF] ✅ Successfully linked variable 'Ua' to system data <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.256 +08:00 [INF] Creating block 'PAC4200EventController' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.256 +08:00 [INF] PAC4200EventController block 'PAC4200EventController' created - no predefined variables <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.256 +08:00 [INF] Adding dynamic property 'alert' to block 'PAC4200EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.257 +08:00 [INF] ✅ Successfully created variable node 'alert' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.258 +08:00 [INF] Successfully write to blockAddress 1 and tag 0. <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.259 +08:00 [INF] Property 'alert' successfully integrated into system at address 1:0 <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.259 +08:00 [INF] Successfully added dynamic property 'alert' to block 'PAC4200EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.476 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.477 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.478 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.480 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.480 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.480 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.480 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.481 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.481 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:05:36.481 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 14:06:51.892 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:52.836 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:52.974 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:52.975 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:52.982 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:52.983 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.096 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.097 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.155 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.184 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.895 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.897 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.898 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.903 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.912 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.913 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.916 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.944 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.945 +08:00 [INF] Creating PAC4200 Model programmatically: PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.946 +08:00 [INF] Creating PAC4200Controller ObjectType with variable templates... <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.946 +08:00 [INF] ✅ ObjectType added to address space with NodeId:  <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.946 +08:00 [INF] ✅ PAC4200Controller ObjectType with variable templates created successfully <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:53.955 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:55.985 +08:00 [INF] Creating block 'PAC4200Controller' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:55.986 +08:00 [INF] Processing PAC4200Controller variables for block 'PAC4200Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:55.987 +08:00 [INF] ✅ Created variable 'Ua' for block 'PAC4200Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:55.989 +08:00 [INF] ✅ Successfully linked variable 'Ua' to system data <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:55.989 +08:00 [INF] Creating block 'PAC4200EventController' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:55.990 +08:00 [INF] Adding dynamic property 'alert' to block 'PAC4200EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:55.991 +08:00 [INF] ✅ Successfully created variable node 'alert' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:55.992 +08:00 [INF] Successfully write to blockAddress 1 and tag 0. <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:55.992 +08:00 [INF] Property 'alert' successfully integrated into system at address 1:0 <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:55.992 +08:00 [INF] Successfully added dynamic property 'alert' to block 'PAC4200EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:56.221 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:56.221 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:56.222 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:56.225 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:56.225 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:56.225 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:56.225 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:56.225 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:56.225 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:09:56.226 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:11:46.850 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:26.789 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:26.845 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:26.845 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:26.850 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:26.851 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:26.972 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:26.972 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:27.012 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:27.034 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:27.693 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:27.695 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:27.696 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:27.701 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:27.709 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:27.711 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:27.714 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:27.744 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:27.744 +08:00 [INF] Creating PAC4200 Model programmatically: PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:27.753 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:29.778 +08:00 [INF] Creating block 'PAC4200Controller' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:29.779 +08:00 [INF] Processing PAC4200Controller variables for block 'PAC4200Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:29.780 +08:00 [INF] ✅ Created variable 'Ua' for block 'PAC4200Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:29.781 +08:00 [INF] ✅ Successfully linked variable 'Ua' to system data <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:29.781 +08:00 [INF] Creating block 'PAC4200EventController' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:29.782 +08:00 [INF] Adding dynamic property 'alert' to block 'PAC4200EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:29.783 +08:00 [INF] ✅ Successfully created variable node 'alert' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:29.784 +08:00 [INF] Successfully write to blockAddress 1 and tag 0. <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:29.784 +08:00 [INF] Property 'alert' successfully integrated into system at address 1:0 <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:29.784 +08:00 [INF] Successfully added dynamic property 'alert' to block 'PAC4200EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:30.040 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:30.040 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:30.041 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:30.044 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:30.044 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:30.045 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:30.045 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:30.045 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:30.045 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:15:30.045 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:08.359 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:35.240 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:35.359 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:35.359 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:35.377 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:35.378 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:35.640 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:35.640 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:35.701 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:35.724 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:40.438 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:40.439 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:40.440 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:40.445 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:40.469 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:40.471 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:40.474 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:40.520 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:40.520 +08:00 [INF] Creating PAC4200 Model programmatically: PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:42.661 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:19:56.654 +08:00 [INF] Creating block 'PAC4200Controller' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:02.043 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:02.180 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:02.180 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:02.187 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:02.188 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:02.321 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:02.321 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:02.367 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:02.395 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:03.024 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:03.025 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:03.027 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:03.032 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:03.041 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:03.043 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:03.046 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:03.071 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:03.071 +08:00 [INF] Creating PAC4200 Model programmatically: PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:03.079 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:05.109 +08:00 [INF] Processing PAC4200Controller variables for block 'PAC4200Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:05.109 +08:00 [INF] ✅ Created variable 'Ua' for block 'PAC4200Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:05.110 +08:00 [INF] ✅ Successfully linked variable 'Ua' to system data <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:05.319 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:05.319 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:05.320 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:05.323 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:05.323 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:05.323 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:05.323 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:05.324 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:05.324 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:05.324 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:24:49.069 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:14.942 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.025 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.025 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.029 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.029 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.109 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.109 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.146 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.167 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.704 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.705 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.706 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.711 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.718 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.719 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.722 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.746 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.746 +08:00 [INF] Creating PAC4200 Model programmatically: PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.747 +08:00 [INF] Creating PAC4200Controller ObjectType with variable templates... <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.747 +08:00 [INF] ✅ ObjectType added to address space with NodeId:  <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.747 +08:00 [INF] ✅ PAC4200Controller ObjectType with variable templates created successfully <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:15.755 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:17.769 +08:00 [INF] Creating block 'PAC4200Controller' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:17.769 +08:00 [ERR] Failed to start PAC4200NodeManager for PAC4200 143213: [BadTypeDefinitionInvalid]. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:18.004 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:18.005 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:18.006 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:18.008 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:18.008 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:18.008 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:18.009 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:18.009 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:18.009 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:18.009 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 16:59:58.663 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:04.544 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:04.602 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:04.602 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:04.605 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:04.606 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:04.698 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:04.699 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:04.737 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:04.758 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:05.337 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:05.339 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:05.340 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:05.346 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:05.353 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:05.354 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:05.358 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:05.384 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:05.384 +08:00 [INF] Creating PAC4200 Model programmatically: PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:05.385 +08:00 [INF] Creating PAC4200Controller ObjectType with variable templates... <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:05.385 +08:00 [INF] ✅ ObjectType added to address space with NodeId:  <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:05.385 +08:00 [INF] ✅ PAC4200Controller ObjectType with variable templates created successfully <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:05.393 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.408 +08:00 [INF] Creating block 'PAC4200Controller' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.409 +08:00 [INF] Processing PAC4200Controller variables for block 'PAC4200Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.410 +08:00 [INF] ✅ Created variable 'Ua' for block 'PAC4200Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.411 +08:00 [INF] ✅ Successfully linked variable 'Ua' to system data <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.411 +08:00 [INF] Creating block 'PAC4200EventController' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.629 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.630 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.631 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.634 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.634 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.634 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.634 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.634 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.634 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:00:07.634 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 17:05:31.260 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:40.002 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:40.145 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:40.146 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:40.154 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:40.155 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:40.280 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:40.281 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:40.340 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:40.369 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:41.187 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:41.189 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:41.191 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:41.197 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:41.207 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:41.209 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:41.213 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:41.250 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:41.260 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:43.281 +08:00 [INF] Creating block 'PAC4200Controller' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:43.283 +08:00 [INF] Creating block 'PAC4200EventController' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:43.285 +08:00 [INF] Processing PAC4200Controller variables for block 'PAC4200Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:43.286 +08:00 [INF] ✅ Created variable 'Ua' for block 'PAC4200Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:43.290 +08:00 [INF] ✅ Successfully linked variable 'Ua' to system data <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:43.292 +08:00 [INF] Adding dynamic property 'alert' to block 'PAC4200EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:43.296 +08:00 [INF] ✅ Successfully created variable node 'alert' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:43.301 +08:00 [INF] Successfully write to blockAddress 1 and tag 0. <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:43.302 +08:00 [INF] Property 'alert' successfully integrated into system at address 1:0 <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:43.302 +08:00 [INF] Successfully added dynamic property 'alert' to block 'PAC4200EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:44.178 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:44.183 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:44.186 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:44.194 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:44.195 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:44.195 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:44.195 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:44.195 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:44.196 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:01:44.196 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:02:12.109 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:13.389 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:13.549 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:13.550 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:13.556 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:13.557 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:13.676 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:13.677 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:13.731 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:13.760 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:14.625 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:14.628 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:14.630 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:14.637 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:14.647 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:14.649 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:14.655 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:14.710 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:14.737 +08:00 [ERR] Failed to deserialize PAC4200 configuration from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
System.InvalidOperationException: There is an error in XML document (2, 2).
 ---> System.InvalidOperationException: <Configuration xmlns=''> was not expected.
   at Microsoft.Xml.Serialization.GeneratedAssembly.XmlSerializationReaderConfiguration.Read5_Configuration()
   --- End of inner exception stack trace ---
   at System.Xml.Serialization.XmlSerializer.Deserialize(XmlReader xmlReader, String encodingStyle, XmlDeserializationEvents events)
   at System.Xml.Serialization.XmlSerializer.Deserialize(Stream stream)
   at OPCUAServer.Devices.PAC4200.PAC4200System.Load() in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200System.cs:line 187
2025-08-04 20:22:14.800 +08:00 [ERR] Failed to start PAC4200NodeManager for PAC4200 143213: There is an error in XML document (2, 2).. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:15.594 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:15.595 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:15.599 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:15.608 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:15.610 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:15.610 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:15.611 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:15.611 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:15.611 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:22:15.612 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:23.121 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:23.203 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:23.204 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:23.207 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:23.208 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:23.336 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:23.336 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:23.386 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:23.434 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:24.167 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:24.169 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:24.172 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:24.178 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:24.189 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:24.191 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:24.196 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:24.234 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:24.246 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:26.270 +08:00 [INF] Creating block 'Controller' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:26.271 +08:00 [INF] Creating block 'EventController' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:26.274 +08:00 [INF] Processing PAC4200Controller variables for block 'Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:26.276 +08:00 [INF] ✅ Created variable 'Ua' for block 'Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:26.279 +08:00 [INF] ✅ Successfully linked variable 'Ua' to system data <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:26.282 +08:00 [INF] Adding dynamic property 'alert' to block 'EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:26.286 +08:00 [INF] ✅ Successfully created variable node 'alert' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:26.291 +08:00 [INF] Successfully write to blockAddress 1 and tag 0. <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:26.292 +08:00 [INF] Property 'alert' successfully integrated into system at address 1:0 <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:26.292 +08:00 [INF] Successfully added dynamic property 'alert' to block 'EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:27.095 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:27.096 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:27.099 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:27.109 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:27.109 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:27.109 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:27.110 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:27.110 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:27.110 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:23:27.111 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:13.157 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:24.704 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:24.784 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:24.785 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:24.788 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:24.789 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:24.910 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:24.911 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:24.962 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:24.990 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:25.713 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:25.714 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:25.716 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:25.723 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:25.734 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:25.736 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:25.740 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:25.775 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:25.783 +08:00 [ERR] Failed to deserialize PAC4200 configuration from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
System.InvalidOperationException: There is an error in XML document (2, 2).
 ---> System.InvalidOperationException: <Configuration xmlns='http://sentron.com'> was not expected.
   at Microsoft.Xml.Serialization.GeneratedAssembly.XmlSerializationReaderConfiguration.Read5_Configuration()
   --- End of inner exception stack trace ---
   at System.Xml.Serialization.XmlSerializer.Deserialize(XmlReader xmlReader, String encodingStyle, XmlDeserializationEvents events)
   at System.Xml.Serialization.XmlSerializer.Deserialize(Stream stream)
   at OPCUAServer.Devices.PAC4200.PAC4200System.Load() in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200System.cs:line 187
2025-08-04 20:24:25.806 +08:00 [ERR] Failed to start PAC4200NodeManager for PAC4200 143213: There is an error in XML document (2, 2).. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:26.088 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:26.088 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:26.090 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:26.093 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:26.094 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:26.094 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:26.094 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:26.094 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:26.094 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:24:26.095 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:08.675 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:08.763 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:08.764 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:08.768 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:08.770 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:08.919 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:08.919 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:08.974 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:09.005 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:09.757 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:09.759 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:09.761 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:09.767 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:09.777 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:09.778 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:09.782 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:09.819 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:09.829 +08:00 [ERR] Failed to deserialize PAC4200 configuration from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
System.InvalidOperationException: There is an error in XML document (2, 2).
 ---> System.InvalidOperationException: <Configuration xmlns='http://sentron.com'> was not expected.
   at Microsoft.Xml.Serialization.GeneratedAssembly.XmlSerializationReaderConfiguration.Read5_Configuration()
   --- End of inner exception stack trace ---
   at System.Xml.Serialization.XmlSerializer.Deserialize(XmlReader xmlReader, String encodingStyle, XmlDeserializationEvents events)
   at System.Xml.Serialization.XmlSerializer.Deserialize(Stream stream)
   at OPCUAServer.Devices.PAC4200.PAC4200System.Load() in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200System.cs:line 187
2025-08-04 20:25:09.852 +08:00 [ERR] Failed to start PAC4200NodeManager for PAC4200 143213: There is an error in XML document (2, 2).. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:10.163 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:10.164 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:10.166 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:10.181 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:10.182 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:10.186 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:10.187 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:10.191 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:10.191 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:10.192 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:34.127 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:41.457 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:41.544 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:41.544 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:41.548 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:41.549 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:41.678 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:41.678 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:41.731 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:41.763 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:42.582 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:42.584 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:42.588 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:42.601 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:42.617 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:42.619 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:42.623 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:42.661 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:42.674 +08:00 [ERR] Failed to deserialize PAC4200 configuration from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
System.InvalidOperationException: There is an error in XML document (2, 2).
 ---> System.InvalidOperationException: <Configuration xmlns='http://sentron.com/Devices'> was not expected.
   at Microsoft.Xml.Serialization.GeneratedAssembly.XmlSerializationReaderConfiguration.Read5_Configuration()
   --- End of inner exception stack trace ---
   at System.Xml.Serialization.XmlSerializer.Deserialize(XmlReader xmlReader, String encodingStyle, XmlDeserializationEvents events)
   at System.Xml.Serialization.XmlSerializer.Deserialize(Stream stream)
   at OPCUAServer.Devices.PAC4200.PAC4200System.Load() in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200System.cs:line 187
2025-08-04 20:25:42.697 +08:00 [ERR] Failed to start PAC4200NodeManager for PAC4200 143213: There is an error in XML document (2, 2).. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:43.005 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:43.006 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:43.008 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:43.014 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:43.014 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:43.015 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:43.015 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:43.016 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:43.017 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:25:43.017 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:29.676 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:38.854 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:38.940 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:38.941 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:38.944 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:38.945 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:39.080 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:39.080 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:39.160 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:39.200 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:39.965 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:39.966 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:39.969 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:39.975 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:39.985 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:39.987 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:39.991 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:40.033 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:40.045 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Devices.PAC4200.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.067 +08:00 [INF] Creating block 'Controller' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.069 +08:00 [INF] Creating block 'EventController' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.072 +08:00 [INF] Processing PAC4200Controller variables for block 'Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.074 +08:00 [INF] ✅ Created variable 'Ua' for block 'Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.079 +08:00 [INF] ✅ Successfully linked variable 'Ua' to system data <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.082 +08:00 [INF] Adding dynamic property 'alert' to block 'EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.086 +08:00 [INF] ✅ Successfully created variable node 'alert' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.092 +08:00 [INF] Successfully write to blockAddress 1 and tag 0. <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.092 +08:00 [INF] Property 'alert' successfully integrated into system at address 1:0 <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.093 +08:00 [INF] Successfully added dynamic property 'alert' to block 'EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.955 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.967 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.970 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.981 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.981 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.982 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.982 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.983 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.983 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:42.984 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:26:59.879 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:39.092 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:39.226 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:39.226 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:39.236 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:39.237 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:39.364 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:39.364 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:39.417 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:39.447 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:40.259 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:40.261 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:40.264 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:40.270 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:40.279 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:40.281 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:40.285 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:40.319 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:42.662 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:42.663 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:42.665 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:42.670 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:42.670 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:42.670 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:42.671 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:42.672 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:42.672 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:33:42.672 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:23.743 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:27.865 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:27.950 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:27.950 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:27.954 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:27.955 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:28.076 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:28.076 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:28.128 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:28.162 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:28.909 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:28.911 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:28.913 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:28.919 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:28.929 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:28.931 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:28.935 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:28.979 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:31.363 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:31.364 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:31.366 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:31.373 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:31.374 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:31.374 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:31.374 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:31.375 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:31.375 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:34:31.375 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-04 20:36:04.466 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
