using OPCUAServer.Common;
using OPCUAServer.Devices.PAC4200;
using Serilog;
using UnifiedAutomation.UaBase;

namespace OPCUAServer.Services;

/// <summary>
/// Service for dynamically adding OPC UA nodes to running server using Timer.
/// This service demonstrates real-time dynamic node addition capabilities.
/// </summary>
public class DynamicNodeService : IDisposable
{
    private static readonly ILogger _logger = LoggingHelper.GetLogger<DynamicNodeService>();
    
    private Timer? _dynamicNodeTimer;
    private readonly List<PAC4200NodeManager> _nodeManagers;
    private int _propertyCounter = 0;
    private bool _isDisposed = false;

    public DynamicNodeService()
    {
        _nodeManagers = new List<PAC4200NodeManager>();
    }

    /// <summary>
    /// Registers a PAC4200NodeManager for dynamic node addition.
    /// </summary>
    /// <param name="nodeManager">The NodeManager to register</param>
    public void RegisterNodeManager(PAC4200NodeManager nodeManager)
    {
        if (nodeManager != null && !_nodeManagers.Contains(nodeManager))
        {
            _nodeManagers.Add(nodeManager);
            _logger.Information($"Registered PAC4200NodeManager: {nodeManager.PAC4200Name}");
        }
    }

    /// <summary>
    /// Starts the dynamic node addition service with specified interval.
    /// </summary>
    /// <param name="intervalSeconds">Interval in seconds between dynamic additions</param>
    public void StartDynamicNodeAddition(int intervalSeconds)
    {
        if (_dynamicNodeTimer != null)
        {
            _logger.Warning("Dynamic node service is already running");
            return;
        }

        _logger.Information($"Starting Dynamic Node Service with {intervalSeconds}s interval");

        _dynamicNodeTimer = new Timer(
            AddDynamicNodeCallback,
            null,
            TimeSpan.FromSeconds(5), // Initial delay of 5 seconds
            TimeSpan.FromSeconds(intervalSeconds));

        _logger.Information("Dynamic Node Service started successfully");
    }

    /// <summary>
    /// Stops the dynamic node addition service.
    /// </summary>
    public void StopDynamicNodeAddition()
    {
        if (_dynamicNodeTimer != null)
        {
            _dynamicNodeTimer.Dispose();
            _dynamicNodeTimer = null;
            _logger.Information("Dynamic Node Service stopped");
        }
    }

    /// <summary>
    /// Timer callback that adds dynamic properties to registered NodeManagers.
    /// </summary>
    private void AddDynamicNodeCallback(object? state)
    {
        try
        {
            if (_nodeManagers.Count == 0)
            {
                _logger.Warning("No NodeManagers registered for dynamic node addition");
                return;
            }

            foreach (var nodeManager in _nodeManagers)
            {
                AddDynamicPropertyToNodeManager(nodeManager);
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"Error in dynamic node addition timer: {ex.Message}");
        }
    }

    /// <summary>
    /// Adds a dynamic property to a specific NodeManager.
    /// </summary>
    private void AddDynamicPropertyToNodeManager(PAC4200NodeManager nodeManager)
    {
        try
        {
            // Get existing blocks
            var existingBlocks = nodeManager.GetExistingBlocks();
            if (existingBlocks.Count == 0)
            {
                _logger.Warning($"No blocks found in NodeManager: {nodeManager.PAC4200Name}");
                return;
            }

            // Use the first block as target
            string targetBlock = existingBlocks[0];
            
            // Generate dynamic property based on counter
            var propertyInfo = GenerateDynamicProperty(_propertyCounter);
            
            _logger.Information($"Adding dynamic property '{propertyInfo.Name}' to block '{targetBlock}' in NodeManager '{nodeManager.PAC4200Name}'");

            // Add the dynamic property
            bool success = nodeManager.AddDynamicProperty(
                blockName: targetBlock,
                propertyName: propertyInfo.Name,
                dataType: propertyInfo.DataType,
                initialValue: propertyInfo.InitialValue,
                isWriteable: propertyInfo.IsWriteable);

            if (success)
            {
                _logger.Information($"✅ Successfully added dynamic property '{propertyInfo.Name}' - Check OPC UA client!");
                _propertyCounter++;
            }
            else
            {
                _logger.Error($"❌ Failed to add dynamic property '{propertyInfo.Name}'");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"Error adding dynamic property to NodeManager '{nodeManager.PAC4200Name}': {ex.Message}");
        }
    }

    /// <summary>
    /// Generates dynamic property information based on counter.
    /// </summary>
    private static DynamicPropertyInfo GenerateDynamicProperty(int counter)
    {
        var propertyTypes = new[]
        {
            new DynamicPropertyInfo("DynamicTemperature", DataTypeIds.Double, 25.5 + (counter * 0.1), false),
            new DynamicPropertyInfo("DynamicPressure", DataTypeIds.Double, 1013.25 + counter, false),
            new DynamicPropertyInfo("DynamicStatus", DataTypeIds.Boolean, counter % 2 == 0, true),
            new DynamicPropertyInfo("DynamicCounter", DataTypeIds.Int32, counter, true),
            new DynamicPropertyInfo("DynamicVoltage", DataTypeIds.Double, 230.0 + (counter * 0.5), false),
            new DynamicPropertyInfo("DynamicCurrent", DataTypeIds.Double, 10.0 + (counter * 0.2), false),
            new DynamicPropertyInfo("DynamicMode", DataTypeIds.String, $"Mode_{counter}", true),
            new DynamicPropertyInfo("DynamicSetpoint", DataTypeIds.Double, 50.0 + counter, true)
        };

        int index = counter % propertyTypes.Length;
        var baseProperty = propertyTypes[index];
        
        // Make property name unique by adding counter
        return new DynamicPropertyInfo(
            $"{baseProperty.Name}_{counter + 1}",
            baseProperty.DataType,
            baseProperty.InitialValue,
            baseProperty.IsWriteable);
    }

    /// <summary>
    /// Adds a batch of test properties immediately (for testing purposes).
    /// </summary>
    public void AddTestPropertiesBatch()
    {
        _logger.Information("Adding batch of test properties...");
        
        foreach (var nodeManager in _nodeManagers)
        {
            var existingBlocks = nodeManager.GetExistingBlocks();
            if (existingBlocks.Count == 0) continue;

            string targetBlock = existingBlocks[0];
            
            var testProperties = new[]
            {
                new DynamicPropertyInfo("TestTemperature", DataTypeIds.Double, 23.5, false),
                new DynamicPropertyInfo("TestHumidity", DataTypeIds.Double, 65.0, false),
                new DynamicPropertyInfo("TestAlarm", DataTypeIds.Boolean, false, true),
                new DynamicPropertyInfo("TestDescription", DataTypeIds.String, "Test Property", true)
            };

            foreach (var prop in testProperties)
            {
                bool success = nodeManager.AddDynamicProperty(
                    targetBlock, prop.Name, prop.DataType, prop.InitialValue, prop.IsWriteable);
                
                if (success)
                {
                    _logger.Information($"✅ Added test property: {prop.Name}");
                }
                else
                {
                    _logger.Error($"❌ Failed to add test property: {prop.Name}");
                }
            }
        }
    }

    /// <summary>
    /// Gets status information about the service.
    /// </summary>
    public string GetServiceStatus()
    {
        return $"DynamicNodeService - Managers: {_nodeManagers.Count}, " +
               $"Properties Added: {_propertyCounter}, " +
               $"Running: {_dynamicNodeTimer != null}";
    }

    public void Dispose()
    {
        if (!_isDisposed)
        {
            StopDynamicNodeAddition();
            _nodeManagers.Clear();
            _isDisposed = true;
            _logger.Information("DynamicNodeService disposed");
        }
    }
}

/// <summary>
/// Information about a dynamic property to be added.
/// </summary>
public record DynamicPropertyInfo(
    string Name,
    NodeId DataType,
    object InitialValue,
    bool IsWriteable);
