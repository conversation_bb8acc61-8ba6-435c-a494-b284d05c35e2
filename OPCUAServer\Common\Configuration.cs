using System.Xml.Serialization;
using UnifiedAutomation.UaBase;

namespace OPCUAServer.Common;

#region Configuration File Classes
[XmlType(TypeName = "ControllerProperty")]
public class ControllerProperty
{
    [XmlElement(Order = 1)]
    public string Name { get; set; }

    [XmlElement(Order = 2)]
    public string DataType { get; set; }

    [XmlElement(Order = 3)]
    public string Value { get; set; }

    [XmlElement(Order = 4)]
    public bool Writeable { get; set; }

    [XmlElement(Order = 5)]
    public string Range { get; set; }
}

[XmlType(TypeName = "ControllerConfiguration")]
public class ControllerConfiguration
{
    [XmlElement(Order = 1)]
    public string Name { get; set; }

    [XmlElement(Order = 2)]
    public int Type { get; set; }

    [XmlElement(Order = 3)]
    public ControllerProperty[] Properties;
}

[XmlRoot(ElementName = "Configuration")]
public class Configuration
{
    [XmlElement(Order = 1)]
    public ControllerConfiguration[] Controllers;
}
#endregion

#region BlockProperty Class
/// <summary>
/// The configuration for a property of a blockAddress.
/// </summary>
public class BlockProperty
{
    public int Offset;
    public string Name;
    public NodeId DataType;
    public bool Writeable;
    public UnifiedAutomation.UaBase.Range Range;
}
#endregion

#region BlockConfiguration Class
/// <summary>
/// The configuration for a blockAddress.
/// </summary>
public class BlockConfiguration
{
    public int Address;
    public string Name;
    public int Type;
    public List<BlockProperty> Properties; // todo: keep consistent with other block configurations
}
#endregion

#region BlockType Class
public class BlockType
{
    public const int PAC4200 = 1;
    public const int PAC4200Event = 2;
}
#endregion



