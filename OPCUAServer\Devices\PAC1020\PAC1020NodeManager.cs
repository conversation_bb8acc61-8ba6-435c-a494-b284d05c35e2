using System.Reflection;
using OPCUAServer.Common;
using RolePermissionBasedAccess;
using Serilog;
using UnifiedAutomation.UaBase;
using UnifiedAutomation.UaServer;

namespace OPCUAServer.Devices.PAC1020;

/// <summary>
/// PAC1020NodeManager is a class that manages the PAC1020 system.
/// </summary>
internal partial class PAC1020NodeManager : BaseNodeManager
{
    private static readonly string PAC1020TemplateFileName = "PAC1020_Modified.xml";
    private static Dictionary<string, PAC1020System> _dictPAC1020System = new();
    private readonly ILogger _logger = LoggingHelper.GetLogger<PAC1020NodeManager>();

    public ushort InstanceNamespaceIndex { get; private set; }

    public ushort TypeNamespaceIndex { get; private set; }

    public string PAC1020ItemId { get; set; } = string.Empty;

    public string PAC1020Name { get; set; } = "PAC1020";

    /// <summary>
    /// Initializes a new instance of the <see cref="PAC1020NodeManager"/> class.
    /// </summary>
    /// <param name="server">The server manager.</param>
    public PAC1020NodeManager(ServerManager server)
        : base(server)
    {
        // Namespace initialization moved to Startup() method
    }

    public static void InitialPAC1020System(int pacNumber, List<string> itemid)
    {
        _dictPAC1020System = itemid.Take(pacNumber)
                                   .ToDictionary(id => id, id => new PAC1020System());
    }

    /// <summary>
    /// Called when the node manager is started.
    /// </summary>
    public override void Startup()
    {
        try
        {
            this._logger.Information("Starting PAC1020NodeManager.");
            base.Startup();

            // Initialize namespaces
            this.TypeNamespaceIndex = this.AddNamespaceUri(Sentron.PAC1020.Namespaces.PAC1020);
            this.InstanceNamespaceIndex = this.AddNamespaceUri("http://sentron.org/PAC1020/" + this.PAC1020Name);

            // load the model.
            this._logger.Information($"Loading the PAC1020 Model: {this.PAC1020Name}.");
            this.ImportUaNodeset(Assembly.GetEntryAssembly(), PAC1020TemplateFileName);

            _dictPAC1020System[this.PAC1020ItemId].Initialize(this.PAC1020ItemId);

            try
            {
                var settings = new CreateObjectSettings()
                {
                    ParentNodeId = ObjectIds.ObjectsFolder,
                    ReferenceTypeId = ReferenceTypeIds.Organizes,
                    RequestedNodeId = new NodeId(this.PAC1020Name, this.InstanceNamespaceIndex),
                    BrowseName = new QualifiedName(this.PAC1020Name, this.InstanceNamespaceIndex),
                    TypeDefinitionId = ObjectTypeIds.FolderType
                };
                this.CreateObject(this.Server.DefaultRequestContext, settings);
            }
            catch (Exception ex)
            {
                this._logger.Error($"Failed to create PAC1020NodeManager: {ex.Message}.");
            }

            // Create controllers from configuration
            foreach (BlockConfiguration block in _dictPAC1020System[this.PAC1020ItemId].GetBlocks())
            {
                // set type definition NodeId
                NodeId typeDefinitionId = ObjectTypeIds.BaseObjectType;
                if (block.Type == BlockType.PAC1020)
                {
                    typeDefinitionId = new NodeId(Sentron.PAC1020.ObjectTypes.PAC1020, this.TypeNamespaceIndex);
                }

                // create object.
                var settings = new CreateObjectSettings()
                {
                    ParentNodeId = new NodeId(this.PAC1020Name, this.InstanceNamespaceIndex),
                    ReferenceTypeId = ReferenceTypeIds.Organizes,
                    RequestedNodeId = new NodeId(block.Name, this.InstanceNamespaceIndex),
                    BrowseName = new QualifiedName(block.Name, this.TypeNamespaceIndex),
                    TypeDefinitionId = typeDefinitionId
                };

                this.CreateObject(this.Server.DefaultRequestContext, settings);

                if (block.Properties != null)
                {
                    foreach (BlockProperty property in block.Properties)
                    {
                        // the node was already created when the controller object was instantiated.
                        // this call links the node to the underlying system data.
                        VariableNode variable = this.SetVariableConfiguration(
                            new NodeId(block.Name, this.InstanceNamespaceIndex),
                            new QualifiedName(property.Name, this.TypeNamespaceIndex),
                            NodeHandleType.ExternalPolled,
                            new SystemAddress() { Address = block.Address, Offset = property.Offset });

                        if (variable != null)
                        {
                            lock (this.InMemoryNodeLock)
                            {
                                variable.AccessLevel = property.Writeable ? AccessLevels.CurrentReadOrWrite : AccessLevels.CurrentRead;
                            }
                        }

                        this.SetNodePermissions(
                            new NodeId(block.Name, this.InstanceNamespaceIndex),
                            new QualifiedName(property.Name, this.TypeNamespaceIndex),
                            RolePermissionManager.RolePermissionTypeCollection);
                    }
                }
            }
        }
        catch (Exception e)
        {
            this._logger.Error($"Failed to start PAC1020 NodeManager: {e.Message}.");
        }
    }

    /// <summary>
    /// Called when the node manager is stopped.
    /// </summary>
    public override void Shutdown()
    {
        try
        {
            this._logger.Information("Stopping PAC1020NodeManager.");
            base.Shutdown();
        }
        catch (Exception e)
        {
            this._logger.Error($"Failed to stop PAC1020 NodeManager: {e.Message}.");
        }
    }

    /// <summary>
    /// Releases unmanaged and - optionally - managed resources.
    /// Disposes the PAC1020 system instance associated with this node manager.
    /// </summary>
    /// <param name="disposing">
    /// <c>true</c> to release both managed and unmanaged resources;
    /// <c>false</c> to release only unmanaged resources.
    /// </param>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _dictPAC1020System[this.PAC1020ItemId]?.Dispose();
        }
    }

    /// <summary>
    /// Reads the attributes.
    /// </summary>
    /// <param name="context">The context.</param>
    /// <param name="transaction">The transaction.</param>
    /// <param name="operationHandles">The operation handles.</param>
    /// <param name="settings">The settings.</param>
    protected override void Read(
        RequestContext context,
        TransactionHandle transaction,
        IList<NodeAttributeOperationHandle> operationHandles,
        IList<ReadValueId> settings)
    {
        for (int ii = 0; ii < operationHandles.Count; ii++)
        {
            DataValue? dv = null;

            if (operationHandles[ii].NodeHandle.UserData is SystemAddress address)
            {
                if (this.CannotPassNodeAccessChecks(context, operationHandles[ii].NodeHandle, UserAccessMask.Read, out StatusCode statusCode))
                {
                    dv = new DataValue(statusCode);
                }
                else
                {
                    object? value = _dictPAC1020System[this.PAC1020ItemId].Read(address.Address, address.Offset);
                    if (value is not null)
                    {
                        dv = new DataValue(new Variant(value, null), DateTime.UtcNow);

                        if (!string.IsNullOrEmpty(settings[ii].IndexRange) || !QualifiedName.IsNull(settings[ii].DataEncoding))
                        {
                            dv = this.ApplyIndexRangeAndEncoding(
                                operationHandles[ii].NodeHandle,
                                dv,
                                settings[ii].IndexRange,
                                settings[ii].DataEncoding);
                        }
                    }
                }
            }

            // set an error if not found.
            dv ??= new DataValue(new StatusCode(StatusCodes.BadNodeIdUnknown));

            ((ReadCompleteEventHandler)transaction.Callback)(
                operationHandles[ii],
                transaction.CallbackData,
                dv,
                true);
        }
    }

    /// <summary>
    /// Write the attributes.
    /// </summary>
    /// <param name="context">The context.</param>
    /// <param name="transaction">The transaction.</param>
    /// <param name="operationHandles">The operation handles.</param>
    /// <param name="settings">The settings.</param>
    protected override void Write(
        RequestContext context,
        TransactionHandle transaction,
        IList<NodeAttributeOperationHandle> operationHandles,
        IList<WriteValue> settings)
    {
        for (int ii = 0; ii < operationHandles.Count; ii++)
        {
            StatusCode error = StatusCodes.BadNodeIdUnknown;

            if (operationHandles[ii].NodeHandle.UserData is SystemAddress address)
            {
                if (!string.IsNullOrEmpty(settings[ii].IndexRange))
                {
                    error = StatusCodes.BadIndexRangeInvalid;
                }
                else if (this.CannotPassNodeAccessChecks(context, operationHandles[ii].NodeHandle, UserAccessMask.Write, out StatusCode statusCode))
                {
                    error = statusCode;
                }
                else if (!_dictPAC1020System[this.PAC1020ItemId].Write(address.Address, address.Offset, settings[ii].Value.Value))
                {
                    error = StatusCodes.BadUserAccessDenied;
                }
                else
                {
                    error = StatusCodes.Good;
                }
            }

            // return the data to the caller.
            ((WriteCompleteEventHandler)transaction.Callback)(
                operationHandles[ii],
                transaction.CallbackData,
                error,
                true);
        }
    }
}
