<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <!-- <StartupObject>PanelOPCUAServer.PasswordHashTest</StartupObject> -->
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Common/license/License.lic" />
    <None Remove="Common/Model/Circuit_Modified.xml" />
    <None Remove="Common/Model/PAC1020_Modified.xml" />
    <None Remove="Common/Model/PAC3120_Modified.xml" />
    <None Remove="Common/Model/PAC3220_Modified.xml" />
    <None Remove="Common/Model/PAC4200_Modified.xml" />
    <None Remove="Common/Model/panelalarm.xml" />
<!--    <None Remove="Common/Model/panelhistoricalmsg.xml" />-->
<!--    <None Remove="Common/Model/Panel_Modified.xml" />-->
    <None Remove="Common/Model/Substation_Modified.xml" />
    <None Remove="Common/Model/ThreeVA_Modified.xml" />
    <None Remove="Common/Model/ThreeWA_Modified.xml" />
    <None Remove="Common/Model/ThreeWL_Modified.xml" />
    <None Remove="Common/Model/WTMS_Modified.xml" />
    <None Remove="Devices/PAC1020/PAC1020Configuration.xml" />
    <None Remove="Devices/PAC3120/PAC3120Configuration.xml" />
    <None Remove="Devices/PAC3220/PAC3220Configuration.xml" />
<!--    <None Remove="Devices/PAC4200/PAC4200Configuration.xml" />-->
    <None Remove="Devices/ThreeVA/ThreeVAConfiguration.xml" />
    <None Remove="Devices/ThreeWA/ThreeWAConfiguration.xml" />
    <None Remove="Devices/ThreeWL/ThreeWLConfiguration.xml" />
    <None Remove="Devices/WTMS/WTMSConfiguration.xml" />
    <None Remove="Devices/Circuit/CircuitConfiguration.xml" />
    <None Remove="Devices/Panel/PanelConfiguration.xml" />
    <None Remove="Devices/Substation/SubstationConfiguration.xml" /> 
    <None Remove="Common/GeneralConfiguration.xml" />
    <None Remove="PanelAlarm/PanelAlarmConfiguration.xml" />
<!--    <None Remove="PanelHistoricalMsg/PanelHistoricalMsgConfiguration.xml" />-->
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Common/Model/Circuit_Modified.xml" />
    <EmbeddedResource Include="Common/Model/PAC3120_Modified.xml" />
<!--    <EmbeddedResource Include="Common/Model/panelhistoricalmsg.xml" />-->
    <None Remove="Common/Model/UserManagement_Modified.xml" />
    <EmbeddedResource Include="Common/Model/UserManagement_Modified.xml" />
    <EmbeddedResource Remove="UserManagementNode/UserManagementConfiguration.xml" />
    <EmbeddedResource Include="UserManagement/UserManagementConfiguration.xml" />
    <None Remove="Common\Model\PanelAlarm_Modified.xml" />
    <EmbeddedResource Include="Common\Model\PanelAlarm_Modified.xml" />
    <EmbeddedResource Include="Common\GeneralConfiguration.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Common/license/License.lic" />
    <EmbeddedResource Include="Common/Model/PAC1020_Modified.xml" />
    <EmbeddedResource Include="Common/Model/PAC3220_Modified.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Common/Model/PAC4200_Modified.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Common/Model/Substation_Modified.xml" />
    <EmbeddedResource Include="Common/Model/ThreeVA_Modified.xml" />
    <EmbeddedResource Include="Common/Model/ThreeWA_Modified.xml" />
    <EmbeddedResource Include="Common/Model/ThreeWL_Modified.xml" />
    <EmbeddedResource Include="Common/Model/WTMS_Modified.xml" />
    <EmbeddedResource Include="Devices/PAC1020/PAC1020Configuration.xml" />
    <EmbeddedResource Include="Devices/PAC3120/PAC3120Configuration.xml" />
    <EmbeddedResource Include="Devices/PAC3220/PAC3220Configuration.xml" />
    <EmbeddedResource Include="Devices/ThreeVA/ThreeVAConfiguration.xml" />
    <EmbeddedResource Include="Devices/ThreeWA/ThreeWAConfiguration.xml" />
    <EmbeddedResource Include="Devices/ThreeWL/ThreeWLConfiguration.xml" />
    <EmbeddedResource Include="Devices/Circuit/CircuitConfiguration.xml" />
    <EmbeddedResource Include="Devices/Panel/PanelConfiguration.xml" />
    <EmbeddedResource Include="Devices/Substation/SubstationConfiguration.xml" />
    <EmbeddedResource Include="Devices/WTMS/WTMSConfiguration.xml" />
    <EmbeddedResource Include="PanelAlarm/PanelAlarmConfiguration.xml" />
<!--    <EmbeddedResource Include="PanelHistoricalMsg/PanelHistoricalMsgConfiguration.xml">-->
<!--      <CopyToOutputDirectory>Always</CopyToOutputDirectory>-->
<!--    </EmbeddedResource>-->
  </ItemGroup>

  <ItemGroup>
      <PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.0" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
      <PackageReference Include="StyleCop.Analyzers" Version="1.2.0-beta.556">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
	  <PackageReference Include="System.Configuration.ConfigurationManager" Version="6.0.0" />
	  
	  <PackageReference Include="Serilog" Version="3.1.1" />
	  <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
	  <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
	  <PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
	  <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.0" />
	  <PackageReference Include="Serilog.Enrichers.Environment" Version="2.3.0" />
	  <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
	  <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
	  <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
	  <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
	  <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
	  <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.0" />
	  <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
  </ItemGroup>

	<ItemGroup>
		<Reference Include="UnifiedAutomation.UaBase">
			<HintPath>libs/UnifiedAutomation.UaBase.dll</HintPath>
			<Private>True</Private>
		</Reference>
		<Reference Include="UnifiedAutomation.UaBase.Windows">
			<HintPath>libs/UnifiedAutomation.UaBase.Windows.dll</HintPath>
			<Private>True</Private>
		</Reference>
		<Reference Include="UnifiedAutomation.UaServer">
			<HintPath>libs/UnifiedAutomation.UaServer.dll</HintPath>
			<Private>True</Private>
		</Reference>
		<Reference Include="UnifiedAutomation.UaBase.BouncyCastle">
			<HintPath>libs/UnifiedAutomation.UaBase.BouncyCastle.dll</HintPath>
			<Private>True</Private>
		</Reference>
	</ItemGroup>

	<ItemGroup>
	  <Content Include="RolePermissionBasedAccess/UserDatabase.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	    <Link>UserDatabase.json</Link>
	  </Content>
	</ItemGroup>

</Project>
