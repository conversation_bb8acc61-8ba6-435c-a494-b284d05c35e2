2025-08-05 09:41:31.616 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:32.350 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:32.351 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:32.420 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:32.421 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:32.690 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:32.690 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:32.754 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:32.775 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:33.582 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:33.583 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:33.584 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:33.591 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:33.612 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:33.614 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:33.618 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:41:33.719 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:42:17.345 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:42:17.346 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:42:17.347 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:42:17.351 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:42:17.351 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:42:17.351 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:42:17.351 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:42:17.352 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:42:17.352 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:42:17.352 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:06.692 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:06.788 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:06.788 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:06.809 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:06.810 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:07.003 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:07.003 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:07.052 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:07.073 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:07.639 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:07.640 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:07.641 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:07.647 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:07.672 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:07.673 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:07.676 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:43:07.739 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:01.414 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:01.507 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:01.507 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:01.529 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:01.530 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:01.734 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:01.734 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:01.787 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:01.810 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:02.387 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:02.389 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:02.390 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:02.395 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:02.419 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:02.421 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:02.424 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:44:02.488 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:27.978 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.079 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.079 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.102 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.103 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.306 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.307 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.356 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.377 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.959 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.961 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.961 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.966 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.991 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.992 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:28.995 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:29.064 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:49.105 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Common.PAC4200Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.704 +08:00 [INF] Creating block 'Controller' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.705 +08:00 [INF] Creating block 'EventController' using BaseObjectType (testing independent variable creation) <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.706 +08:00 [INF] Processing PAC4200Controller variables for block 'Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.706 +08:00 [INF] ✅ Created variable 'Ua' for block 'Controller' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.708 +08:00 [INF] ✅ Successfully linked variable 'Ua' to system data <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.709 +08:00 [INF] Adding dynamic property 'alert' to block 'EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.709 +08:00 [INF] ✅ Successfully created variable node 'alert' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.711 +08:00 [INF] Successfully write to blockAddress 1 and tag 0. <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.711 +08:00 [INF] Property 'alert' successfully integrated into system at address 1:0 <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.711 +08:00 [INF] Successfully added dynamic property 'alert' to block 'EventController' <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.915 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.915 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.916 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.918 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.918 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.918 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.918 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.918 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.919 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 09:47:53.919 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:50.522 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:51.512 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:51.512 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:51.602 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:51.603 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:51.700 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:51.700 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:51.741 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:51.768 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:52.330 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:52.331 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:52.333 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:52.339 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:52.346 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:52.347 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:52.350 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:52.373 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:54.572 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:54.572 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:54.573 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:54.577 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:54.577 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:54.577 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:54.577 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:54.577 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:54.578 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:01:54.578 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:00.931 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:08.743 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:08.850 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:08.850 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:08.874 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:08.875 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:09.095 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:09.095 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:09.147 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:09.168 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:09.785 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:09.787 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:09.788 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:09.795 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:09.824 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:09.826 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:09.829 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:09.902 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:35.577 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:35.629 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:35.629 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:35.632 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:35.632 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:35.712 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:35.712 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:35.748 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:35.769 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.273 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.274 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.276 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.281 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.288 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.289 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.292 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.317 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.325 +08:00 [INF] PAC4200 configuration loaded successfully from OPCUAServer.Common.Configuration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.328 +08:00 [ERR] Failed to start PAC4200NodeManager for PAC4200 143213: Value cannot be null. (Parameter 'key'). <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.515 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.516 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.517 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.520 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.520 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.520 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.520 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.520 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.520 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:36.521 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:04:59.341 +08:00 [INF] Stopping Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:08.875 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:08.969 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:08.970 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:08.991 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:08.992 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:09.185 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:09.185 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:09.234 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:09.259 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:09.862 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:09.863 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:09.863 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:09.870 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:09.896 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:09.897 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:09.900 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:05:09.967 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:46.719 +08:00 [INF] Serilog Logging System Initialized <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:46.816 +08:00 [INF] === OPC UA Server Started === <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:46.817 +08:00 [INF] Initializing OPC UA Server... <> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:46.838 +08:00 [INF] Starting OPC UA Server... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:46.839 +08:00 [INF] Loading License File... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:47.035 +08:00 [INF] License File Loaded Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:47.035 +08:00 [INF] Creating OPCServerManager Instance... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:47.086 +08:00 [INF] OPCServerManager Instance Created Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:47.116 +08:00 [INF] Starting OPC UA Application... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:47.705 +08:00 [INF] Device list:System.Collections.Generic.List`1[PanelHttp.IDeviceInfo] loaded successfully <PanelHttp.ReadPanelHttp> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:47.706 +08:00 [INF] Starting UserManagementNodeManager. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:47.707 +08:00 [INF] Loading the Controller Model. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:47.711 +08:00 [INF] Initializing the UserManagementSystem. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:47.733 +08:00 [INF] Creating the UserManagement Folder. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:47.734 +08:00 [INF] Creating the UserManagement Controllers. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:47.737 +08:00 [INF] Setting the method user data for the UserManagementController controller. <OPCUAServer.UserManagement.UserManagementNodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:06:47.796 +08:00 [INF] Starting PAC4200NodeManager for PAC4200 143213. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:07:20.359 +08:00 [ERR] Failed to deserialize PAC4200 configuration from OPCUAServer.UserManagement.UserManagementConfiguration.xml <OPCUAServer.Devices.PAC4200.PAC4200System> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
System.InvalidOperationException: There is an error in XML document (2, 2).
 ---> System.InvalidOperationException: <UserManagementSystemConfiguration xmlns='http://sentron.com/usermanagementsystem'> was not expected.
   at Microsoft.Xml.Serialization.GeneratedAssembly.XmlSerializationReaderConfiguration.Read5_Configuration()
   --- End of inner exception stack trace ---
   at System.Xml.Serialization.XmlSerializer.Deserialize(XmlReader xmlReader, String encodingStyle, XmlDeserializationEvents events)
   at System.Xml.Serialization.XmlSerializer.Deserialize(Stream stream)
   at OPCUAServer.Devices.PAC4200.PAC4200System.Load() in C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC4200\PAC4200System.cs:line 188
2025-08-05 10:07:20.406 +08:00 [ERR] Failed to start PAC4200NodeManager for PAC4200 143213: There is an error in XML document (2, 2).. <OPCUAServer.Devices.PAC4200.PAC4200NodeManager> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:07:20.613 +08:00 [INF] OPC UA Application Started Successfully <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:07:20.614 +08:00 [INF] Listening at the following endpoints: <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:07:20.614 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:07:20.616 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Basic256Sha256:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:07:20.616 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:07:20.616 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes128Sha256RsaOaep:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:07:20.616 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [SignAndEncrypt:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:07:20.616 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [Sign:Aes256Sha256RsaPss:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:07:20.617 +08:00 [INF]    opc.tcp://dinm5CG5031HHV:48030/ [None:None:Binary]: Status=Good <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
2025-08-05 10:07:20.617 +08:00 [INF] Server Started, Press Enter to Exit... <OPCUAServer.OpcUAServer> {"MachineName":"DINM5CG5031HHV","ThreadId":1,"Application":"OPC UA Server","Version":"1.0.0"}
