using OPCUAServer.Common;
using OPCUAServer.Devices.PAC4200;
using OPCUAServer.Services;
using Serilog;

namespace OPCUAServer.Examples;

/// <summary>
/// Example showing how to integrate dynamic node addition into your existing OPC UA server.
/// This demonstrates the Timer-based approach for adding properties to running server.
/// </summary>
public class DynamicNodeExample
{
    private static readonly ILogger _logger = LoggingHelper.GetLogger<DynamicNodeExample>();

    /// <summary>
    /// Example 1: Basic integration after server startup
    /// Call this method after your OPC UA server has started and NodeManagers are initialized.
    /// </summary>
    public static void BasicIntegrationExample(List<PAC4200NodeManager> pac4200NodeManagers)
    {
        _logger.Information("=== Basic Dynamic Node Integration Example ===");

        try
        {
            DynamicNodeServiceIntegration.StartDynamicNodeService(pac4200NodeManagers, 120);

            _logger.Information("Dynamic node service started!");
            _logger.Information("New properties will be added every 30 seconds");
            _logger.Information("Monitor your OPC UA client to see new nodes appear");
        }
        catch (Exception ex)
        {
            _logger.Error($"Failed to start dynamic node service: {ex.Message}");
        }
    }

    /// <summary>
    /// Example 2: Quick test - add properties immediately
    /// Use this for immediate testing without waiting for timer.
    /// </summary>
    public static void QuickTestExample(PAC4200NodeManager nodeManager)
    {
        _logger.Information("=== Quick Test Example ===");

        try
        {
            // Add a single test property immediately
            bool success = nodeManager.AddTestProperty("QuickTestProperty");
            
            if (success)
            {
                _logger.Information("✅ Quick test property added - check your OPC UA client!");
            }
            else
            {
                _logger.Error("❌ Failed to add quick test property");
            }

            // Add a batch of test properties
            var testService = new DynamicNodeService();
            testService.RegisterNodeManager(nodeManager);
            testService.AddTestPropertiesBatch();
            testService.Dispose();

            _logger.Information("✅ Test batch completed - check your OPC UA client!");
        }
        catch (Exception ex)
        {
            _logger.Error($"Quick test failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Example 3: Custom timer intervals and property types
    /// Shows how to customize the dynamic addition behavior.
    /// </summary>
    public static void CustomTimerExample(List<PAC4200NodeManager> nodeManagers)
    {
        _logger.Information("=== Custom Timer Example ===");

        try
        {
            // Start with faster interval for demonstration (10 seconds)
            DynamicNodeServiceIntegration.StartDynamicNodeService(nodeManagers, 10);

            _logger.Information("Custom timer started with 10-second intervals");
            _logger.Information("Properties will be added more frequently");

            // You can also stop and restart with different intervals
            Task.Run(async () =>
            {
                await Task.Delay(60000); // Wait 1 minute
                
                _logger.Information("Changing to slower interval...");
                DynamicNodeServiceIntegration.StopDynamicNodeService();
                
                await Task.Delay(2000); // Brief pause
                
                DynamicNodeServiceIntegration.StartDynamicNodeService(nodeManagers, 60);
                _logger.Information("Now using 60-second intervals");
            });
        }
        catch (Exception ex)
        {
            _logger.Error($"Custom timer example failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Example 4: Integration with your existing server startup code
    /// This shows how to modify your existing Program.cs or server initialization.
    /// </summary>
    public static void ServerStartupIntegrationExample()
    {
        _logger.Information("=== Server Startup Integration Example ===");
        _logger.Information("");
        _logger.Information("Add this code to your existing server startup:");
        _logger.Information("");
        _logger.Information("// After your server starts and NodeManagers are created:");
        _logger.Information("var pac4200Managers = new List<PAC4200NodeManager>();");
        _logger.Information("");
        _logger.Information("// Collect your PAC4200NodeManager instances");
        _logger.Information("// (modify this to match your actual code)");
        _logger.Information("foreach (var itemId in pac4200ItemIds)");
        _logger.Information("{");
        _logger.Information("    var nodeManager = GetPAC4200NodeManager(itemId);");
        _logger.Information("    if (nodeManager != null)");
        _logger.Information("        pac4200Managers.Add(nodeManager);");
        _logger.Information("}");
        _logger.Information("");
        _logger.Information("// Start dynamic node service");
        _logger.Information("DynamicNodeServiceIntegration.StartDynamicNodeService(pac4200Managers, 30);");
        _logger.Information("");
        _logger.Information("// Optional: Add immediate test properties");
        _logger.Information("DynamicNodeServiceIntegration.AddTestPropertiesNow();");
    }

    /// <summary>
    /// Example 5: Console interface for interactive testing
    /// Use this for manual testing and debugging.
    /// </summary>
    public static void ConsoleInterfaceExample(List<PAC4200NodeManager> nodeManagers)
    {
        _logger.Information("=== Console Interface Example ===");
        _logger.Information("Starting interactive console for dynamic node testing...");

        // This will start an interactive console interface
        DynamicNodeConsoleHelper.StartConsoleInterface(nodeManagers);
    }

    /// <summary>
    /// Example 6: Monitoring and status checking
    /// Shows how to monitor the dynamic node service.
    /// </summary>
    public static void MonitoringExample()
    {
        _logger.Information("=== Monitoring Example ===");

        // Start a monitoring timer
        var monitorTimer = new Timer(MonitorCallback, null, TimeSpan.Zero, TimeSpan.FromSeconds(15));

        _logger.Information("✅ Monitoring started - status will be logged every 15 seconds");

        // Keep monitoring for 2 minutes, then stop
        Task.Run(async () =>
        {
            await Task.Delay(120000); // 2 minutes
            monitorTimer.Dispose();
            _logger.Information("🛑 Monitoring stopped");
        });
    }

    private static void MonitorCallback(object? state)
    {
        string status = DynamicNodeServiceIntegration.GetServiceStatus();
        _logger.Information($"📊 Service Status: {status}");
    }

    /// <summary>
    /// Complete example showing full lifecycle
    /// </summary>
    public static async Task CompleteLifecycleExample(List<PAC4200NodeManager> nodeManagers)
    {
        _logger.Information("=== Complete Lifecycle Example ===");

        try
        {
            // 1. Start the service
            _logger.Information("1️⃣ Starting dynamic node service...");
            DynamicNodeServiceIntegration.StartDynamicNodeService(nodeManagers, 20);

            // 2. Add immediate test properties
            _logger.Information("2️⃣ Adding immediate test properties...");
            DynamicNodeServiceIntegration.AddTestPropertiesNow();

            // 3. Wait and monitor
            _logger.Information("3️⃣ Monitoring for 1 minute...");
            for (int i = 0; i < 4; i++)
            {
                await Task.Delay(15000); // Wait 15 seconds
                string status = DynamicNodeServiceIntegration.GetServiceStatus();
                _logger.Information($"📊 Status: {status}");
            }

            // 4. Stop the service
            _logger.Information("4️⃣ Stopping dynamic node service...");
            DynamicNodeServiceIntegration.StopDynamicNodeService();

            _logger.Information("✅ Complete lifecycle example finished!");
        }
        catch (Exception ex)
        {
            _logger.Error($"Lifecycle example failed: {ex.Message}");
        }
    }
}
