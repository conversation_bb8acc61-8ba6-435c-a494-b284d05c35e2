<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile>OPCUAServer.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2c374c9a-6955-4ccb-8c78-79f32dd1220f" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/Common/LoggingHelper.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Common/LoggingHelper.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Common/RegisterValue.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Common/RegisterValue.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Common/SystemAddress.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Common/SystemAddress.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataSource/Interface/IDeviceInfo.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataSource/Interface/IDeviceInfo.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataSource/PanelDeviceRtData.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataSource/PanelDeviceRtData.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataSource/ReadPanelHttp.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataSource/ReadPanelHttp.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DataSource/ReadPanelMessage.cs" beforeDir="false" afterPath="$PROJECT_DIR$/DataSource/ReadPanelMessage.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/Circuit/CircuitNodeManager.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/Circuit/CircuitNodeManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/Circuit/CircuitSystem.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/Circuit/CircuitSystem.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/PAC1020/PAC1020NodeManager.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/PAC1020/PAC1020NodeManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/PAC1020/PAC1020System.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/PAC1020/PAC1020System.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/PAC3120/PAC3120NodeManager.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/PAC3120/PAC3120NodeManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/PAC3120/PAC3120System.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/PAC3120/PAC3120System.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/PAC3220/PAC3220Configuration.cs" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/PAC3220/PAC3220Configuration.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/PAC3220/PAC3220NodeManager.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/PAC3220/PAC3220NodeManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/PAC3220/PAC3220System.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/PAC3220/PAC3220System.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/PAC4200/PAC4200Configuration.cs" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/PAC4200/PAC4200Configuration.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/PAC4200/PAC4200NodeManager.cs" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/PAC4200/PAC4200System.cs" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/Panel/PanelNodeManager.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/Panel/PanelNodeManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/Panel/PanelSystem.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/Panel/PanelSystem.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/Substation/SubstationNodeManager.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/Substation/SubstationNodeManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/Substation/SubstationSystem.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/Substation/SubstationSystem.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/ThreeVA/ThreeVANodeManager.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/ThreeVA/ThreeVANodeManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/ThreeVA/ThreeVASystem.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/ThreeVA/ThreeVASystem.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/ThreeWA/ThreeWANodeManager.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/ThreeWA/ThreeWANodeManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/ThreeWA/ThreeWASystem.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/ThreeWA/ThreeWASystem.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/ThreeWL/ThreeWLNodeManager.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/ThreeWL/ThreeWLNodeManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/ThreeWL/ThreeWLSystem.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/ThreeWL/ThreeWLSystem.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/WTMS/WTMSNodeManager.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/WTMS/WTMSNodeManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Devices/WTMS/WTMSSystem.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Devices/WTMS/WTMSSystem.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Examples/DynamicNodeExample.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Examples/DynamicNodeExample.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/OPCServerManager.cs" beforeDir="false" afterPath="$PROJECT_DIR$/OPCServerManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/OPCUAServer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/OPCUAServer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/OPCUAServer.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/OPCUAServer.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RolePermissionBasedAccess/LinqUserDatabase.cs" beforeDir="false" afterPath="$PROJECT_DIR$/RolePermissionBasedAccess/LinqUserDatabase.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Services/DynamicNodeService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Services/DynamicNodeService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Services/DynamicNodeServiceIntegration.cs" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/UserManagement/UserManagementNodeManager.cs" beforeDir="false" afterPath="$PROJECT_DIR$/UserManagement/UserManagementNodeManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/UserManagement/UserManagementSystem.cs" beforeDir="false" afterPath="$PROJECT_DIR$/UserManagement/UserManagementSystem.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Utilites/DataTypeConvert.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Utilites/DataTypeConvert.cs" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/0f2f3a78e83c4468a55cd7a1c9d061d523000/6f/68cd005e/ILogger.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6ef916de40f74bf2aa0bd8f29ff0a9976a9000/20/db3ca2f7/StatusCode.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6ef916de40f74bf2aa0bd8f29ff0a9976a9000/8c/935cbb40/ReferenceTypeIds.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/8c2fe80be7bf4d60a91943f23727af18f9600/b1/c4ebe7c3/BaseNodeManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/51/2e4b7092/List`1.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/83/c1ee140f/Monitor.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/cb/a6f7e27a/ExecutionContext.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/OPCServerManager.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="Toolset" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30K7OlbqdotRupCmOXqzaiQT44K" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    ".NET Project.OPCUAServer.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "feature/alerting2__deleteModifiedxml",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.sourceCode.C#",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="OPCUAServer" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/OPCUAServer.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2c374c9a-6955-4ccb-8c78-79f32dd1220f" name="Changes" comment="" />
      <created>1753364381816</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753364381816</updated>
      <workItem from="1753364383186" duration="64000" />
      <workItem from="1753364479669" duration="3319000" />
      <workItem from="1753407071971" duration="16965000" />
      <workItem from="1753621189843" duration="3186000" />
      <workItem from="1753681635801" duration="4474000" />
      <workItem from="1753704127574" duration="2897000" />
      <workItem from="1753714236661" duration="774000" />
      <workItem from="1753749642259" duration="2423000" />
      <workItem from="1753752808405" duration="1749000" />
      <workItem from="1753789740712" duration="20892000" />
      <workItem from="1753930538940" duration="853000" />
      <workItem from="1754010248334" duration="3201000" />
      <workItem from="1754270474891" duration="5884000" />
      <workItem from="1754294969481" duration="2940000" />
      <workItem from="1754307531983" duration="10266000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/DataSource/PanelDataSource/PanelDataSourceLayer.cs</url>
          <line>243</line>
          <properties documentPath="C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\DataSource\PanelDataSource\PanelDataSourceLayer.cs" containingFunctionPresentation="Method 'StartReadUdcHttp'" />
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Services/DynamicNodeService.cs</url>
          <line>106</line>
          <properties documentPath="C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Services\DynamicNodeService.cs" containingFunctionPresentation="Method 'AddDynamicPropertyToNodeManager'">
            <startOffsets>
              <option value="3321" />
            </startOffsets>
            <endOffsets>
              <option value="3374" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="15" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Services/DynamicNodeService.cs</url>
          <line>122</line>
          <properties documentPath="C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Services\DynamicNodeService.cs" containingFunctionPresentation="Method 'AddDynamicPropertyToNodeManager'">
            <startOffsets>
              <option value="4025" />
            </startOffsets>
            <endOffsets>
              <option value="4322" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Services/DynamicNodeService.cs</url>
          <line>129</line>
          <properties documentPath="C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Services\DynamicNodeService.cs" containingFunctionPresentation="Method 'AddDynamicPropertyToNodeManager'">
            <startOffsets>
              <option value="4336" />
            </startOffsets>
            <endOffsets>
              <option value="4348" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="26" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Devices/PAC3220/PAC3220System.cs</url>
          <line>35</line>
          <properties documentPath="C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC3220\PAC3220System.cs" containingFunctionPresentation="Method 'Initialize'">
            <startOffsets>
              <option value="1005" />
            </startOffsets>
            <endOffsets>
              <option value="1017" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="36" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Devices/PAC3220/PAC3220System.cs</url>
          <line>172</line>
          <properties documentPath="C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC3220\PAC3220System.cs" containingFunctionPresentation="Method 'Load'">
            <startOffsets>
              <option value="5311" />
            </startOffsets>
            <endOffsets>
              <option value="5379" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="37" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Devices/PAC3220/PAC3220System.cs</url>
          <line>209</line>
          <properties documentPath="C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC3220\PAC3220System.cs" containingFunctionPresentation="Method 'Load'">
            <startOffsets>
              <option value="6978" />
            </startOffsets>
            <endOffsets>
              <option value="7027" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="38" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Devices/PAC3220/PAC3220NodeManager.cs</url>
          <line>60</line>
          <properties documentPath="C:\Users\<USER>\Desktop\repo\opcua-server-1\OPCUAServer\Devices\PAC3220\PAC3220NodeManager.cs" containingFunctionPresentation="Method 'Startup'">
            <startOffsets>
              <option value="2121" />
            </startOffsets>
            <endOffsets>
              <option value="2191" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="39" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>